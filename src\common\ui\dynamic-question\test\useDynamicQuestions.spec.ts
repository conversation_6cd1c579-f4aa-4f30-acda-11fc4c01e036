import { mockAuditQuestionsMentalHealthMap } from "@/common/ui/dynamic-question/test/mockServerQuestions";
import { Question } from "@/common/ui/dynamic-question/models/dynamic-question-models";
import { useDynamicQuestions, ActionRegistry } from "@/common/ui/dynamic-question/models/useDynamicQuestions";
import { mockNonCliniAssessment } from "@/calls/common/dynamic-questions/fcms/non-clini-pre-assessment/mock/mockNonCliniPreAssessment";

describe("useDynamicQuestions", () => {
  it("convertFailedContacts", () => {
    const mockQuestions = mockAuditQuestionsMentalHealthMap;
    const questions: Question[] = Object.values(mockQuestions);

    const dynamicQuestions = useDynamicQuestions();
    dynamicQuestions.init(questions);

    expect(dynamicQuestions.state.questions.length).toEqual(6);

    // Second Question should be hidden
    expect(dynamicQuestions.state.questions[1].id).toEqual(
      "safeguardingReferral"
    );
    expect(typeof dynamicQuestions.state.questions[1].condition).toEqual(
      "function"
    );

    // second question should be hidden for now...
    expect(dynamicQuestions.state.questions[1].visible).toEqual(false);

    // Third Question should be hidden
    expect(dynamicQuestions.state.questions[2].id).toEqual(
      "primaryPresentingNeed"
    );
    expect(typeof dynamicQuestions.state.questions[2].condition).toEqual(
      "function"
    );

    // third question should be hidden for now...
    expect(dynamicQuestions.state.questions[2].visible).toEqual(false);

    // now lets answer the first question
    dynamicQuestions.onQuestionAnswered({
      id: "safeguardingConcerns",
      type: "radio",
      label:
        "Q1 - Do you have any safeguarding concerns relating to the current consultation?",
      options: ["Yes", "No"],
      mandatory: true,
      value: "Yes",
      visible: true
    });

    // check answer has been set.
    const answers: Record<string, unknown> = dynamicQuestions.state.answers;
    expect(answers["safeguardingConcerns"]).toEqual("Yes");

    // check the condition of the second question
    const condition = dynamicQuestions.state.questions[1].condition as (
      answers: Record<string, unknown>
    ) => boolean;

    // the function should return true
    expect(condition(answers)).toEqual(true);

    // ... so the second question should now be visible
    expect(dynamicQuestions.state.questions[1].visible).toEqual(true);

    // should be the same for the third question
    const condition2 = dynamicQuestions.state.questions[2].condition as (
      answers: Record<string, unknown>
    ) => boolean;
    expect(condition2(answers)).toEqual(true);
    expect(dynamicQuestions.state.questions[2].visible).toEqual(true);

    // which question ids are visible
    const visibleQuestions = dynamicQuestions.state.questions.filter(
      question => question.visible
    );
    const visibleQuestionsIds = visibleQuestions.map(q => q.id);
    expect(visibleQuestionsIds.length).toEqual(6);

    // validateAnswers
    const missingQuestions = dynamicQuestions.validateAnswers();

    // since only 1st question has been answered, the rest should be missing
    expect(missingQuestions.length).toEqual(5);

    const missingQuestionsIds = missingQuestions.map(q => q.id);

    expect(missingQuestionsIds).toEqual([
      "safeguardingReferral",
      "primaryPresentingNeed",
      "levelOfIntervention",
      "onwardPathway",
      "supportAndLiaisonRequired"
    ]);

    // what answers do we have.
    expect(dynamicQuestions.state.answers).toEqual({
      safeguardingConcerns: "Yes"
    });
  });

  it("should trigger actions when question conditions are met", () => {
    // Mock action registry
    const actionRegistry: ActionRegistry = {
      showEndOfLifeMessage: jest.fn(),
      completeCaseWithOutcome: jest.fn(),
      setPriorityAndAllowSave: jest.fn()
    };

    const dynamicQuestions = useDynamicQuestions(actionRegistry);

    // Find Q4 (patientEndOfLifeCare) from the mock
    const q4Question = mockNonCliniAssessment.find(q => q.id === "patientEndOfLifeCare");
    expect(q4Question).toBeDefined();
    expect(q4Question!.actions).toBeDefined();

    // Initialize with the mock questions
    dynamicQuestions.init(mockNonCliniAssessment);

    // Answer Q4 with "Yes" to trigger the action
    dynamicQuestions.onQuestionAnswered({
      ...q4Question!,
      value: "Yes"
    });

    // Check that the action was called with correct params
    expect(actionRegistry.showEndOfLifeMessage).toHaveBeenCalledWith({
      message: "Please follow the local policy for managing patients who are receiving end of life care",
      completePages: true
    });

    // Find Q6 (patientCall999)
    const q6Question = mockNonCliniAssessment.find(q => q.id === "patientCall999");
    expect(q6Question).toBeDefined();

    // Answer Q6 with "Patient/Caller WILL call 999"
    dynamicQuestions.onQuestionAnswered({
      ...q6Question!,
      value: "Patient/Caller WILL call 999"
    });

    // Check that the corresponding action was called
    expect(actionRegistry.completeCaseWithOutcome).toHaveBeenCalledWith({
      outcome: "Patient called 999",
      promptComplete: true
    });

    // Answer Q6 with "Patient/Caller DECLINED 999"
    dynamicQuestions.onQuestionAnswered({
      ...q6Question!,
      value: "Patient/Caller DECLINED 999"
    });

    // Check that the other action was called
    expect(actionRegistry.setPriorityAndAllowSave).toHaveBeenCalledWith({
      priority: "20 minutes",
      allowSave: true
    });
  });
});
