<template>
  <form @submit.prevent="submitForm">
    <div class="ic24-flex-column">
      <div v-for="question in dynamicQuestionsController.state.questions" :key="question.id">
        <!--        <div class="ic24-flex-row ic24-flex-gap">{{ question.visible }}</div>-->
        <DynamicQuestion v-if="question.visible" class="dynamic-question-form--question" :value="question"
          @input="onInput" />
      </div>
    </div>

    <div>
      <div v-for="question in getQuestionAnswerValuesDebug.value" v-text="question" :key="question"></div>
    </div>

    {{ dynamicQuestionsController.state.answers }}

    <!--    <button type="submit">Submit</button>-->
  </form>
</template>

<script lang="ts">
import {
  defineComponent,
  PropType,
  reactive,
  SetupContext,
  watch
} from "@vue/composition-api";
import { Question } from "@/common/ui/dynamic-question/models/dynamic-question-models";
import DynamicQuestion from "@/common/ui/dynamic-question/ui/DynamicQuestion.vue";
import { useDynamicQuestions } from "@/common/ui/dynamic-question/models/useDynamicQuestions";
import { computed } from "@vue/reactivity";

export default defineComponent({
  name: "DynamicQuestionForm",
  components: { DynamicQuestion },
  props: {
    questions: {
      type: Array as PropType<Question[]>,
      required: true
    },
    debug: {
      type: Boolean,
      default: false
    }
  },
  setup(props: { questions: Question[], debug: boolean }, context: SetupContext) {
    const dynamicQuestionsController = useDynamicQuestions(
      reactive({
        questions: [],
        answers: {},
        currentQuestionId: null,
        questionFlow: [],
        usingNextFunctionality: false
      })
    );

    dynamicQuestionsController.init(props.questions);

    // watch question changes and update the controller
    watch(
      () => props.questions,
      newQuestions => {
        dynamicQuestionsController.init(newQuestions);
      }
    );

    function onInput(question: Question) {
      dynamicQuestionsController.onQuestionAnswered(question);
      const output = dynamicQuestionsController.getOutput();
      console.log("DynamicQuestionForm onInput:", output);
      context.emit("input", output);
    }

    function goToPreviousQuestion() {
      dynamicQuestionsController.goToPreviousQuestion();
      const output = dynamicQuestionsController.getOutput();
      context.emit("input", output);
    }

    function goToQuestion(questionId: string) {
      dynamicQuestionsController.goToQuestion(questionId);
      const output = dynamicQuestionsController.getOutput();
      context.emit("input", output);
    }


    const getQuestionAnswerValuesDebug = computed(() => {
      return dynamicQuestionsController.state.questions.map((question) => {
        return question.label + "=" + question.value?.toString()
      })
    })


    return {
      dynamicQuestionsController,
      getQuestionAnswerValuesDebug,

      onInput,
      goToPreviousQuestion,
      goToQuestion
    };
  }
});
</script>

<style scoped>
.error {
  color: red;
  font-size: 0.9em;
}

.dynamic-question-form--question {
  margin-bottom: var(--ic24-flex-gap-large);
}
</style>
