# Memory Bank Setup Instructions

This document contains the setup instructions for initializing the Memory Bank feature in the CLEO Frontend project.

## Project Overview

CLEO is a Vue.js 2 healthcare frontend application serving NHS emergency and non-emergency services. It provides real-time call handling, patient assessment through PACCS (Patient Assessment and Clinical Care System), telephony integration via SESUI, and emergency dispatch coordination.

## Key Technologies

- **Frontend**: Vue.js 2.6.14 with TypeScript 4.1.5
- **State Management**: Vuex 3.6.2 with modular architecture
- **Real-time Communication**: Microsoft SignalR 5.0.2 and WebSocket
- **UI Framework**: Tailwind CSS with custom healthcare-specific styling
- **Data Grids**: AG Grid Community 24.1.0 for call management displays
- **Build System**: Vue CLI 4.5.10 with Webpack optimization

## Core Business Domains

### PACCS (Patient Assessment and Clinical Care System)
- Clinical triage and decision support
- Multi-tab symptom assessment interface
- Integration with Pathways clinical decision support
- Complete audit trail for clinical governance

### SESUI (Telephony Service Integration)
- WebSocket-based telephony service
- Real-time call state management
- Operator status tracking and call control

### Call Grids System
- Real-time dashboard for multiple service types (111, CAS, Oversight, Base)
- AG Grid-based data visualization
- SignalR integration for live updates

### Legacy Integration
- Adapter pattern for bridging legacy CLEO systems
- Global variable watching for menu navigation
- Bidirectional communication between old and new systems

## Architecture Patterns

- **Modular Domain-Driven Design**: Each healthcare domain as self-contained module
- **Legacy Integration Adapter Pattern**: Seamless integration with existing systems
- **Real-time Communication Architecture**: WebSocket and SignalR for live updates
- **Component-Based Architecture**: Vue.js single file components with TypeScript

## Development Context

The system is actively developed with focus on:
- Performance optimization for large datasets in call grids
- Enhanced error handling and recovery mechanisms
- Vue 3 migration planning while maintaining legacy compatibility
- NHS compliance and clinical governance requirements

## Clinical Governance Requirements

- Complete audit trails for all clinical decisions
- NHS Digital security compliance
- Patient data protection and GDPR compliance
- Standardized clinical assessment pathways
- Real-time performance monitoring

This Memory Bank provides comprehensive context for understanding the CLEO healthcare system architecture, business requirements, and technical implementation details.
