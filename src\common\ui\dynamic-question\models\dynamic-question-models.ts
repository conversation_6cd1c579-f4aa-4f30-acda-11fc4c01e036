export type QuestionType = "select" | "radio" | "checkbox" | "text";

export interface QuestionOption {
  label: string;
  value: string | number | boolean;
}

export interface QuestionAction {
  condition: string | ((answer: unknown, answers: Record<string, unknown>) => boolean);
  action: string; // Action name from registry
  params?: Record<string, unknown>; // Optional parameters for the action
}

export interface Question {
  id: string;
  type: "select" | "radio" | "checkbox" | "text";
  label: string;
  helpText?: string; // some help text to further explain.  Appears under field.
  options?: string[] | QuestionOption[]; // Updated to support objects
  mandatory: boolean;
  value?: null | string | number | boolean | string[]; // Allow arrays for checkboxes
  visible: boolean;
  condition?: string | ((answers: Record<string, unknown>) => boolean); // Updated to support string, e.g. http response from server.
  // if condition is a string, it will be converted to a function, e.g. new Function("answers", `return ${answers['q1] === true}`) as any;

  // New property for controlling question flow
  nextQuestion?:
    | string
    | ((answer: unknown, answers: Record<string, unknown>) => string | null); // Returns next question ID or null to end

  // New property for triggering actions based on answers
  actions?: QuestionAction[];

  // Why would we want to make a question hidden and keep the answer???  Probably not needed.
  //  clearOnHidden?: boolean;
}

export interface DynamicQuestionsState {
  questions: Question[];
  answers: Record<string, unknown>;
  currentQuestionId: string | null; // Track the current question for navigation
  questionFlow: string[]; // Ordered list of question IDs that should be visible
  usingNextFunctionality: boolean;  // ew have 2 ways of doing: using the "condition" to decide 
  // questionsVisibility: Record<string, boolean>;
}

export interface DynamicQuestionsOutput extends DynamicQuestionsState {
  isValid: boolean;
  questionsThatNeedAnswer: Question[];
}
