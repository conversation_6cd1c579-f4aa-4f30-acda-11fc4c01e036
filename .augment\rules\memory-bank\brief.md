# CLEO Frontend - Project Brief

## Project Overview

CLEO is a Vue.js 2 healthcare frontend application that serves as a modern interface for emergency medical services and healthcare call management. The system integrates with legacy CLEO backend systems to provide real-time call handling, patient assessment, and clinical decision support.

## Core Objectives

- **Emergency Call Management**: Handle incoming emergency and non-emergency healthcare calls with real-time data processing
- **Clinical Decision Support**: Provide PACCS (Patient Assessment and Clinical Care System) triage functionality for healthcare professionals
- **Legacy System Integration**: Seamlessly integrate with existing CLEO backend infrastructure while modernizing the user interface
- **Real-time Communication**: Support telephony integration through SESUI (telephony service) and WebSocket connections
- **Healthcare Workflow Optimization**: Streamline clinical workflows for 111 services, ambulance dispatch, and patient care coordination

## Key Business Value

The CLEO frontend enables healthcare professionals to:
- Efficiently triage patients using standardized clinical pathways
- Access real-time patient information and call data
- Coordinate emergency response and ambulance dispatch
- Maintain comprehensive audit trails for clinical decisions
- Integrate with NHS systems and patient databases

## Target Users

- **Clinical Call Handlers**: NHS 111 operators handling patient calls
- **Clinicians**: Healthcare professionals conducting patient assessments
- **Dispatchers**: Emergency service coordinators managing ambulance deployment
- **Supervisors**: Healthcare managers overseeing call center operations

## System Context

CLEO operates within the UK's NHS infrastructure, handling critical healthcare communications and patient safety decisions. The system must maintain high availability, data security, and clinical governance standards while providing an intuitive user experience for healthcare professionals working under time pressure.
