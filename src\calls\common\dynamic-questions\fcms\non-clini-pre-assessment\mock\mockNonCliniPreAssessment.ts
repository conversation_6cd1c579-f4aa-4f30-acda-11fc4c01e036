/*
Question Flow Summary:

Q1: Are you with the patient?

Yes → Go to Q3

No → Go to Q2

Q2: Can we contact the patient directly?

Yes → Complete algorithm questions

Supporting text: “Keep the caller on the line until successful contact with the patient has been made – if successful contact cannot be made, select No to this question.”

No → Go to Q3

Q3: Is the caller a Clinician?

Yes → Go to Q12

No → Go to Q4

Q4: Is the patient on end-of-life care?

Yes → Show message: “Please follow the local policy for managing patients who are receiving end of life care”
→ Present non-clinical complete pages

No → Go to Q5

Q5: Is the patient breathing and conscious?

Yes → Go to Q7

No → Go to Q6

Q6: Is the Patient/Caller happy to dial 999? (Dropdown, default blank)

Patient/Caller WILL call 999

Prompt user to complete case

Save outcome as: “Patient called 999”

Patient/Caller DECLINED 999

Automatically set priority to 20 minutes

Allow user to save and close case

Q7: Has the patient had any major blood loss in the last 30 minutes?

Yes → Go to Q6

No → Go to Q8

Q8: Is the patient too breathless to speak in complete sentences?

Yes → Q8a: Is the patient normally this breathless?

Yes → Go to Q9

No → Go to Q6

No → Go to Q9

Q9: Has the caller described any chest pain?

Yes → Go to Q10

No → Go to Q11

Q10: Has the caller described the chest pain as a crushing or severe aching pain radiating to the arm, neck, jaw or shoulder?

Yes → Go to Q6

No → Go to Q11

Q11: Has the caller described any stroke-like symptoms?

Supporting text: (FAST TEST – FACE, ARM, SPEECH, TIME)

Yes → Go to Q6

No → Go to Q12

Q12: Please select Priority

Options:

20 minutes

30 minutes

1 hour

2 hours

4 hours

6 hours

12 hours
*/


import { Question } from "@/common/ui/dynamic-question/models/dynamic-question-models";

export const mockNonCliniAssessment: Question[] = [
  {
    id: "withPatient",
    type: "radio",
    label: "Q1: Are you with the patient?",
    options: ["Yes", "No"],
    mandatory: true,
    value: null,
    visible: true,
    nextQuestion: (answer: unknown) =>
      answer === "Yes" ? "callerIsClinician" : "canContactPatientDirectly"
  },
  {
    id: "canContactPatientDirectly",
    type: "radio",
    label: "Q2: Can we contact the patient directly?",
    options: ["Yes", "No"],
    mandatory: true,
    value: null,
    visible: false,
    nextQuestion: (answer: unknown) => answer === "Yes" ? null : "callerIsClinician",
    helpText:
      "Keep the caller on the line until successful contact with the patient has been made – if successful contact cannot be made, select No to this question."
  },
  {
    id: "callerIsClinician",
    type: "radio",
    label: "Q3: Is the caller a Clinician?",
    options: ["Yes", "No"],
    mandatory: true,
    value: null,
    visible: false,
    nextQuestion: (answer: unknown, answers: Record<string, unknown>) => {
      if (answer === "Yes") {
        return "selectPriority"; // Clinicians go directly to priority selection
      } else {
        return "patientEndOfLifeCare"; // Non-clinicians check end-of-life care
      }
    }
  },
  {
    id: "patientEndOfLifeCare",
    type: "radio",
    label: "Q4: Is the patient on end-of-life care?",
    options: ["Yes", "No"],
    mandatory: true,
    value: null,
    visible: false,
    nextQuestion: (answer: unknown) =>
      answer === "Yes" ? null : "patientBreathingConscious", // End if yes, continue if no
    helpText:
      "If Yes: Please follow the local policy for managing patients who are receiving end of life care",
    actions: [
      {
        condition: (answer: unknown) => answer === "Yes",
        action: "showEndOfLifeMessage",
        params: {
          message: "Please follow the local policy for managing patients who are receiving end of life care",
          completePages: true
        }
      }
    ]
  },
  {
    id: "patientBreathingConscious",
    type: "radio",
    label: "Q5: Is the patient breathing and conscious?",
    options: ["Yes", "No"],
    mandatory: true,
    value: null,
    visible: false,
    nextQuestion: (answer: unknown) => answer === "Yes" ? "majorBloodLoss" : "patientCall999"
  },
  {
    id: "patientCall999",
    type: "select",
    label: "Q6: Is the Patient/Caller happy to dial 999?",
    options: [
      "",
      "Patient/Caller WILL call 999",
      "Patient/Caller DECLINED 999"
    ],
    mandatory: true,
    value: "",
    visible: false,
    nextQuestion: (answer: unknown) => null,
    actions: [
      {
        condition: (answer: unknown) => answer === "Patient/Caller WILL call 999",
        action: "completeCaseWithOutcome",
        params: {
          outcome: "Patient called 999",
          promptComplete: true
        }
      },
      {
        condition: (answer: unknown) => answer === "Patient/Caller DECLINED 999",
        action: "setPriorityAndAllowSave",
        params: {
          priority: "20 minutes",
          allowSave: true
        }
      }
    ]
  },
  {
    id: "majorBloodLoss",
    type: "radio",
    label:
      "Q7: Has the patient had any major blood loss in the last 30 minutes?",
    options: ["Yes", "No"],
    mandatory: true,
    value: null,
    visible: false,
    nextQuestion: (answer: unknown) => answer === "Yes" ? "patientCall999" : "tooBreathless"
  },
  {
    id: "tooBreathless",
    type: "radio",
    label: "Q8: Is the patient too breathless to speak in complete sentences?",
    options: ["Yes", "No"],
    mandatory: true,
    value: null,
    visible: false,
    nextQuestion: (answer: unknown) => answer === "Yes" ? "normallyBreathless" : "chestPain"
  },
  {
    id: "normallyBreathless",
    type: "radio",
    label: "Q8a: Is the patient normally this breathless?",
    options: ["Yes", "No"],
    mandatory: true,
    value: null,
    visible: false,
    nextQuestion: (answer: unknown) => answer === "Yes" ? "chestPain" : "patientCall999"
  },
  {
    id: "chestPain",
    type: "radio",
    label: "Q9: Has the caller described any chest pain?",
    options: ["Yes", "No"],
    mandatory: true,
    value: null,
    visible: false,
    nextQuestion: (answer: unknown) => answer === "Yes" ? "crushingChestPain" : "strokeSymptoms"
  },
  {
    id: "crushingChestPain",
    type: "radio",
    label:
      "Q10: Has the caller described the chest pain as a crushing or severe aching pain radiating to the arm, neck, jaw or shoulder?",
    options: ["Yes", "No"],
    mandatory: true,
    value: null,
    visible: false,
    nextQuestion: (answer: unknown) => answer === "Yes" ? "patientCall999" : "strokeSymptoms"
  },
  {
    id: "strokeSymptoms",
    type: "radio",
    label: "Q11: Has the caller described any stroke-like symptoms?",
    options: ["Yes", "No"],
    mandatory: true,
    value: null,
    visible: false,
    nextQuestion: (answer: unknown) => answer === "Yes" ? "patientCall999" : "selectPriority",
    helpText: "FAST TEST – FACE, ARM, SPEECH, TIME"
  },
  {
    id: "selectPriority",
    type: "select",
    label: "Q12: Please select Priority",
    options: [
      "",
      "20 minutes",
      "30 minutes",
      "1 hour",
      "2 hours",
      "4 hours",
      "6 hours",
      "12 hours"
    ],
    mandatory: true,
    value: "",
    visible: false
  }
];
