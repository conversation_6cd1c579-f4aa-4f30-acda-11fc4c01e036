/**
 * Server-transmittable version of Non-Clinical Pre-Assessment questions
 *
 * This file contains the same question data as mockNonCliniPreAssessment.ts
 * but with functions converted to string expressions for JSON serialization.
 *
 * Use this format when serving questions from a server API.
 */

import { Question } from "@/common/ui/dynamic-question/models/dynamic-question-models";

export interface ServerQuestionAction {
  condition: string;
  action: string;
  params?: Record<string, unknown>;
}

export interface ServerQuestion {
  id: string;
  type: "radio" | "select" | "checkbox" | "text";
  label: string;
  options?: string[];
  mandatory: boolean;
  value: null | string | string[];
  visible: boolean;
  condition?: string;
  nextQuestion?: string;
  helpText?: string;
  actions?: ServerQuestionAction[];
}

export const mockNonCliniAssessmentServer: ServerQuestion[] = [
  {
    id: "withPatient",
    type: "radio",
    label: "Q1: Are you with the patient?",
    options: ["Yes", "No"],
    mandatory: true,
    value: null,
    visible: true,
    nextQuestion:
      "answer === 'Yes' ? 'callerIsClinician' : 'canContactPatientDirectly'"
  },
  {
    id: "canContactPatientDirectly",
    type: "radio",
    label: "Q2: Can we contact the patient directly?",
    options: ["Yes", "No"],
    mandatory: true,
    value: null,
    visible: false,
    condition: "answers['withPatient'] === 'No'",
    nextQuestion: "'callerIsClinician'",
    helpText:
      "Keep the caller on the line until successful contact with the patient has been made – if successful contact cannot be made, select No to this question."
  },
  {
    id: "callerIsClinician",
    type: "radio",
    label: "Q3: Is the caller a Clinician?",
    options: ["Yes", "No"],
    mandatory: true,
    value: null,
    visible: false,
    condition:
      "(answers['withPatient'] === 'Yes') || (answers['withPatient'] === 'No' && answers['canContactPatientDirectly'] === 'No')",
    nextQuestion: "answer === 'Yes' ? 'selectPriority' : 'patientEndOfLifeCare'"
  },
  {
    id: "patientEndOfLifeCare",
    type: "radio",
    label: "Q4: Is the patient on end-of-life care?",
    options: ["Yes", "No"],
    mandatory: true,
    value: null,
    visible: false,
    condition: "answers['callerIsClinician'] === 'No'",
    nextQuestion: "answer === 'Yes' ? null : 'patientBreathingConscious'",
    helpText:
      "If Yes: Please follow the local policy for managing patients who are receiving end of life care",
    actions: [
      {
        condition: "answer === 'Yes'",
        action: "showEndOfLifeMessage",
        params: {
          message: "Please follow the local policy for managing patients who are receiving end of life care",
          completePages: true
        }
      }
    ]
  },
  {
    id: "patientBreathingConscious",
    type: "radio",
    label: "Q5: Is the patient breathing and conscious?",
    options: ["Yes", "No"],
    mandatory: true,
    value: null,
    visible: false,
    condition: "answers['patientEndOfLifeCare'] === 'No'"
  },
  {
    id: "patientCall999",
    type: "select",
    label: "Q6: Is the Patient/Caller happy to dial 999?",
    options: ["Patient/Caller WILL call 999", "Patient/Caller DECLINED 999"],
    mandatory: true,
    value: null,
    visible: false,
    condition:
      "answers['patientBreathingConscious'] === 'No' || answers['majorBloodLoss'] === 'Yes' || (answers['tooBreathless'] === 'Yes' && answers['normallyBreathless'] === 'No') || answers['crushingChestPain'] === 'Yes' || answers['strokeSymptoms'] === 'Yes'",
    actions: [
      {
        condition: "answer === 'Patient/Caller WILL call 999'",
        action: "completeCaseWithOutcome",
        params: {
          outcome: "Patient called 999",
          promptComplete: true
        }
      },
      {
        condition: "answer === 'Patient/Caller DECLINED 999'",
        action: "setPriorityAndAllowSave",
        params: {
          priority: "20 minutes",
          allowSave: true
        }
      }
    ]
  },
  {
    id: "majorBloodLoss",
    type: "radio",
    label:
      "Q7: Has the patient had any major blood loss in the last 30 minutes?",
    options: ["Yes", "No"],
    mandatory: true,
    value: null,
    visible: false,
    condition: "answers['patientBreathingConscious'] === 'Yes'"
  },
  {
    id: "tooBreathless",
    type: "radio",
    label: "Q8: Is the patient too breathless to speak in complete sentences?",
    options: ["Yes", "No"],
    mandatory: true,
    value: null,
    visible: false,
    condition: "answers['majorBloodLoss'] === 'No'"
  },
  {
    id: "normallyBreathless",
    type: "radio",
    label: "Q8a: Is the patient normally this breathless?",
    options: ["Yes", "No"],
    mandatory: true,
    value: null,
    visible: false,
    condition: "answers['tooBreathless'] === 'Yes'"
  },
  {
    id: "chestPain",
    type: "radio",
    label: "Q9: Has the caller described any chest pain?",
    options: ["Yes", "No"],
    mandatory: true,
    value: null,
    visible: false,
    condition:
      "(answers['tooBreathless'] === 'No') || (answers['tooBreathless'] === 'Yes' && answers['normallyBreathless'] === 'Yes')"
  },
  {
    id: "crushingChestPain",
    type: "radio",
    label:
      "Q10: Has the caller described the chest pain as a crushing or severe aching pain radiating to the arm, neck, jaw or shoulder?",
    options: ["Yes", "No"],
    mandatory: true,
    value: null,
    visible: false,
    condition: "answers['chestPain'] === 'Yes'"
  },
  {
    id: "strokeSymptoms",
    type: "radio",
    label: "Q11: Has the caller described any stroke-like symptoms?",
    options: ["Yes", "No"],
    mandatory: true,
    value: null,
    visible: false,
    condition:
      "(answers['chestPain'] === 'No') || (answers['chestPain'] === 'Yes' && answers['crushingChestPain'] === 'No')",
    helpText: "FAST TEST – FACE, ARM, SPEECH, TIME"
  },
  {
    id: "selectPriority",
    type: "select",
    label: "Q12: Please select Priority",
    options: [
      "20 minutes",
      "30 minutes",
      "1 hour",
      "2 hours",
      "4 hours",
      "6 hours",
      "12 hours"
    ],
    mandatory: true,
    value: null,
    visible: false,
    condition:
      "answers['callerIsClinician'] === 'Yes' || (answers['strokeSymptoms'] === 'No')"
  }
];

/**
 * Utility function to convert server questions back to client format
 * This would typically be used in your API service layer
 */
export function parseServerQuestions(
  serverQuestions: ServerQuestion[]
): Question[] {
  return serverQuestions.map(q => {
    const question: Question = { ...q };

    // Convert nextQuestion string to function
    if (q.nextQuestion) {
      question.nextQuestion = new Function(
        "answer",
        "answers",
        `return ${q.nextQuestion};`
      ) as any;
    }

    // Convert condition string to function
    if (q.condition) {
      question.condition = new Function(
        "answers",
        `return ${q.condition};`
      ) as any;
    }

    // Convert actions conditions from strings to functions
    if (q.actions) {
      question.actions = q.actions.map(action => ({
        ...action,
        condition: new Function(
          "answer",
          "answers",
          `return ${action.condition};`
        ) as any
      }));
    }

    return question;
  });
}

/**
 * Example API response format
 * This shows how the questions would be sent from a server
 */
export const exampleApiResponse = {
  success: true,
  data: {
    questions: mockNonCliniAssessmentServer,
    metadata: {
      version: "1.0",
      assessmentType: "NON_CLINICAL_PRE_ASSESSMENT",
      lastUpdated: "2025-01-20T10:00:00Z"
    }
  }
};

/**
 * Example usage in a service
 */
export class QuestionService {
  async fetchNonClinicalAssessment(): Promise<Question[]> {
    // Simulate API call
    const response = await fetch("/api/questions/non-clinical-assessment");
    const data = await response.json();

    // Parse server format to client format
    return parseServerQuestions(data.questions);
  }
}
